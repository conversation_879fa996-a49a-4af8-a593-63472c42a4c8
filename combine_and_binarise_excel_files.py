#!/usr/bin/env python
"""
Excel File Combiner Script

This script combines multiple Excel files containing particle analysis data.
It combines data both worksheet-wise (by particle type/porosity combinations)
and creates a total summary of all data.

Expected worksheet naming convention:
- {Shape}_{Porosity} (e.g., Round_NonPorous, Satellite_Porous, etc.)
- Summary sheets are ignored during processing

Usage:
    python combine_excel_files.py [folder_path]
    
If no folder_path is provided, the script will prompt for one.
"""

import os
import sys
import pandas as pd
from pathlib import Path
import argparse
from collections import defaultdict
import subprocess


def find_xlsx_files(folder_path):
    """
    Find all Excel files in the specified folder.
    
    Args:
        folder_path (str): Path to the folder to search
        
    Returns:
        list: List of Excel file paths
    """
    folder = Path(folder_path)
    if not folder.exists():
        raise FileNotFoundError("Folder '{}' does not exist".format(folder_path))
    
    xlsx_files = list(folder.glob("*.xlsx"))

    # Filter out previously generated combined files
    xlsx_files = [f for f in xlsx_files if not f.name.startswith("combined_")]

    if not xlsx_files:
        raise ValueError("No Excel files (.xlsx) found in folder '{}'".format(folder_path))

    print("Found {} Excel files:".format(len(xlsx_files)))
    for file in xlsx_files:
        print("  - {}".format(file.name))
    
    return xlsx_files


def get_worksheet_data(file_path):
    """
    Extract data from all relevant worksheets in an Excel file.

    Args:
        file_path (Path): Path to the Excel file

    Returns:
        dict: Dictionary mapping worksheet names to DataFrames
    """
    print("Processing file: {}".format(file_path.name))

    try:
        # Read all sheets from the Excel file
        excel_file = pd.ExcelFile(file_path)
        worksheet_data = {}

        for sheet_name in excel_file.sheet_names:
            # Skip summary sheets
            if 'summary' in sheet_name.lower():
                print("  Skipping summary sheet: {}".format(sheet_name))
                continue

            try:
                # Read the worksheet
                df = pd.read_excel(file_path, sheet_name=sheet_name)

                # Skip empty worksheets
                if df.empty:
                    print("  Skipping empty sheet: {}".format(sheet_name))
                    continue

                # Handle case where first row might be headers
                # Check if first row contains column names
                if len(df) > 1 and df.iloc[0].astype(str).str.contains('Area|Perim|Major|Minor').any():
                    # First row contains headers, use it
                    new_columns = df.iloc[0].astype(str).tolist()
                    # Ensure unique column names
                    seen = set()
                    unique_columns = []
                    for col in new_columns:
                        if col in seen:
                            counter = 1
                            while "{}_{}".format(col, counter) in seen:
                                counter += 1
                            col = "{}_{}".format(col, counter)
                        seen.add(col)
                        unique_columns.append(col)

                    df.columns = unique_columns
                    df = df.drop(df.index[0]).reset_index(drop=True)
                    print("  Used first data row as column headers")

                # Add source filename column
                df['Source_File'] = file_path.name

                worksheet_data[sheet_name] = df
                print("  Loaded sheet '{}' with {} rows".format(sheet_name, len(df)))

            except Exception as e:
                print("  Warning: Could not read sheet '{}': {}".format(sheet_name, str(e)))
                continue

        return worksheet_data

    except Exception as e:
        print("Error reading file '{}': {}".format(file_path.name, str(e)))
        return {}


def combine_worksheets(all_files_data):
    """
    Combine data from all files by worksheet type.

    Args:
        all_files_data (list): List of dictionaries containing worksheet data from each file

    Returns:
        tuple: (combined_worksheets_dict, total_summary_df)
    """
    print("\nCombining worksheets...")

    # Dictionary to store combined data for each worksheet type
    combined_worksheets = defaultdict(list)
    all_data_for_summary = []

    # Process each file's data
    for file_data in all_files_data:
        for sheet_name, df in file_data.items():
            # Add to the appropriate worksheet combination
            combined_worksheets[sheet_name].append(df)
            # Add to total summary (without redundant worksheet type column)
            df_copy = df.copy()
            all_data_for_summary.append(df_copy)

    # Combine DataFrames for each worksheet type
    final_combined_worksheets = {}
    for sheet_name, df_list in combined_worksheets.items():
        if df_list:
            combined_df = pd.concat(df_list, ignore_index=True)
            # Split Group column into Porosity and Shape for individual worksheets
            combined_df = split_group_column(combined_df, is_total_summary=False)
            final_combined_worksheets[sheet_name] = combined_df
            print("  Combined '{}': {} total rows from {} files".format(
                sheet_name, len(combined_df), len(df_list)))

    # Create total summary
    if all_data_for_summary:
        total_summary = pd.concat(all_data_for_summary, ignore_index=True)
        # Split Group column into Porosity and Shape for total summary
        total_summary = split_group_column(total_summary, is_total_summary=True)

        # Remove Particle_ID and Source_File columns from total summary
        columns_to_remove = ['Particle_ID', 'Source_File']
        for col in columns_to_remove:
            if col in total_summary.columns:
                total_summary = total_summary.drop(col, axis=1)
                print("    Removed '{}' column from total summary".format(col))

        print("  Created total summary: {} total rows".format(len(total_summary)))
    else:
        total_summary = pd.DataFrame()
        print("  Warning: No data found for total summary")

    return final_combined_worksheets, total_summary


def split_group_column(df, is_total_summary=False):
    """
    Split the Group column into separate Porosity and Shape columns.

    Args:
        df (DataFrame): DataFrame containing a Group column
        is_total_summary (bool): If True, replace "Satellite" and "Splattered" with "Imperfect" in Shape column

    Returns:
        DataFrame: DataFrame with Group column split into Porosity and Shape
    """
    if 'Group' in df.columns:
        # Split the Group column (e.g., "Round_NonPorous" -> "Round", "NonPorous")
        group_parts = df['Group'].str.split('_', expand=True)

        if group_parts.shape[1] >= 2:
            df['Shape'] = group_parts[0]
            df['Porosity'] = group_parts[1]

            # For total summary, replace "Satellite" and "Splattered" with "Imperfect"
            if is_total_summary:
                df['Shape'] = df['Shape'].replace({'Satellite': 'Imperfect', 'Splattered': 'Imperfect'})

            # Remove the original Group column
            #df = df.drop('Group', axis=1)

            print("    Split Group column into Shape and Porosity")
        else:
            print("    Warning: Could not split Group column - unexpected format")

    return df


def save_csv_and_convert_to_arff(total_summary, base_output_path):
    """
    Save the total summary as CSV and convert to ARFF using WEKA CLI.

    Args:
        total_summary (DataFrame): Total summary DataFrame
        base_output_path (str): Base path for output files (without extension)

    Returns:
        tuple: (csv_path, arff_path, success)
    """
    if total_summary.empty:
        print("  Warning: No data to save as CSV/ARFF")
        return None, None, False

    # Generate file paths
    csv_path = base_output_path.replace('.xlsx', '_total_summary.csv')
    arff_path = base_output_path.replace('.xlsx', '_total_summary.arff')

    try:
        # Save as CSV with comma separator
        print("  Saving total summary as CSV: {}".format(os.path.basename(csv_path)))
        total_summary.to_csv(csv_path, index=False, sep=',')
        print("    CSV saved with {} rows and {} columns".format(len(total_summary), len(total_summary.columns)))

        # Convert CSV to ARFF using WEKA CLI
        print("  Converting CSV to ARFF using WEKA CLI...")

        # Use shell=True on Windows to handle redirection properly
        if os.name == 'nt':  # Windows
            cmd_string = 'java weka.core.converters.CSVLoader "{}" > "{}"'.format(csv_path, arff_path)
            result = subprocess.run(cmd_string, shell=True, capture_output=True, text=True)
        else:  # Unix-like systems
            result = subprocess.run(
                ['java', 'weka.core.converters.CSVLoader', csv_path],
                stdout=open(arff_path, 'w'),
                stderr=subprocess.PIPE,
                text=True
            )

        if result.returncode == 0:
            print("    ARFF conversion successful: {}".format(os.path.basename(arff_path)))
            return csv_path, arff_path, True
        else:
            print("    Warning: WEKA conversion failed with return code {}".format(result.returncode))
            if result.stderr:
                print("    Error output: {}".format(result.stderr.strip()))
            return csv_path, None, False

    except subprocess.CalledProcessError as e:
        print("    Warning: WEKA conversion failed: {}".format(str(e)))
        return csv_path, None, False
    except Exception as e:
        print("    Error during CSV/ARFF conversion: {}".format(str(e)))
        return None, None, False


def save_combined_file(combined_worksheets, total_summary, output_path):
    """
    Save the combined data to a new Excel file with auto-sized columns.

    Args:
        combined_worksheets (dict): Dictionary of combined worksheet DataFrames
        total_summary (DataFrame): Total summary DataFrame
        output_path (str): Path for the output file
    """
    print("\nSaving combined file: {}".format(output_path))

    try:
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # Write individual worksheet combinations
            for sheet_name, df in combined_worksheets.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                print("  Saved sheet '{}' with {} rows".format(sheet_name, len(df)))

            # Write total summary
            if not total_summary.empty:
                total_summary.to_excel(writer, sheet_name='Total_Summary', index=False)
                print("  Saved 'Total_Summary' sheet with {} rows".format(len(total_summary)))

            # Auto-size columns for all worksheets
            print("  Auto-sizing columns...")
            workbook = writer.book
            for worksheet_name in workbook.sheetnames:
                worksheet = workbook[worksheet_name]

                # Auto-size each column
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter

                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass

                    # Set column width with some padding (minimum 10, maximum 50)
                    adjusted_width = min(max(max_length + 1, 10), 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

                print("    Auto-sized columns for sheet '{}'".format(worksheet_name))

        print("Successfully saved combined file!")

    except Exception as e:
        print("Error saving file: {}".format(str(e)))
        raise


def main():
    """Main function to orchestrate the Excel file combination process."""
    parser = argparse.ArgumentParser(description='Combine multiple Excel files by worksheets')
    parser.add_argument('folder_path', nargs='?', help='Path to folder containing Excel files')
    args = parser.parse_args()
    
    # Get folder path
    if args.folder_path:
        folder_path = args.folder_path
    else:
        folder_path = input("Enter the path to the folder containing Excel files: ").strip()
    
    try:
        # Find Excel files
        xlsx_files = find_xlsx_files(folder_path)
        
        # Process each file
        all_files_data = []
        for file_path in xlsx_files:
            file_data = get_worksheet_data(file_path)
            if file_data:  # Only add if we got some data
                all_files_data.append(file_data)
        
        if not all_files_data:
            print("Error: No valid data found in any Excel files")
            return
        
        # Combine all data
        combined_worksheets, total_summary = combine_worksheets(all_files_data)
        
        if not combined_worksheets and total_summary.empty:
            print("Error: No data to combine")
            return
        
        # Generate output filename
        folder_name = Path(folder_path).name
        if not folder_name or folder_name == '.':
            folder_name = "excel"

        # Create a timestamp for uniqueness
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = "combined_{}_{}.xlsx".format(folder_name, timestamp)
        output_path = os.path.join(folder_path, output_filename)
        
        # Save combined file
        save_combined_file(combined_worksheets, total_summary, output_path)

        # Save CSV and convert to ARFF if total summary exists
        csv_path = None
        arff_path = None
        if not total_summary.empty:
            print("\nConverting total summary to CSV and ARFF formats...")
            csv_path, arff_path, conversion_success = save_csv_and_convert_to_arff(total_summary, output_path)

        # Print summary
        print("\n" + "="*60)
        print("COMBINATION COMPLETE")
        print("="*60)
        print("Input folder: {}".format(folder_path))
        print("Files processed: {}".format(len(all_files_data)))
        print("Worksheet types found: {}".format(len(combined_worksheets)))
        print("Output file: {}".format(output_path))

        # Show additional output files if they were created
        if csv_path:
            print("CSV file: {}".format(csv_path))
        if arff_path:
            print("ARFF file: {}".format(arff_path))
        
        if combined_worksheets:
            print("\nWorksheet breakdown:")
            for sheet_name, df in combined_worksheets.items():
                print("  {}: {} rows".format(sheet_name, len(df)))
        
        if not total_summary.empty:
            print("Total summary: {} rows".format(len(total_summary)))

            # Show file contribution statistics (only if Source_File column exists)
            if 'Source_File' in total_summary.columns:
                print("\nFile contribution breakdown:")
                file_counts = total_summary['Source_File'].value_counts()
                for filename, count in file_counts.items():
                    print("  {}: {} rows".format(filename, count))
            else:
                print("\nNote: Source file tracking removed from total summary")
        
    except Exception as e:
        print("Error: {}".format(str(e)))
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
