"""
FIJI/ImageJ Compact Batch Particle Analysis Script

Capabilities:
- User-defined paths via environment variables or manual configuration
- Interactive folder selection when input directory not specified
- Batch image processing with particle classification (images are colored based on classification results, optionally with a legend)
- Dual classification with separate models for porosity and shape
- Option to show or hide images during processing (hiding is ~ 3x faster)
- CSV export with statistics

REQUIRED CONFIGURATION:
Set these environment variables before running or specify the paths in the script directly:
- PARTICLE_ANALYSIS_MACRO: Path to ImageJ macro file extracting the needed parameters of the examined particles
- PARTICLE_ANALYSIS_POROSITY_MODEL: Path to porosity classification model
- PARTICLE_ANALYSIS_SHAPE_MODEL: Path to shape classification model
- PARTICLE_ANALYSIS_INPUT_DIR: Directory containing images (or use interactive selection)

OPTIONAL CONFIGURATION:
- WEKA_JAR_PATH: Path to Weka JAR if not in ImageJ classpath
- PARTICLE_ANALYSIS_FILE_TYPES: File extensions (default: tif)
- PARTICLE_ANALYSIS_POROSITY_FEATURES: Features for porosity model (depends on the trained model)
- PART<PERSON>LE_ANALYSIS_SHAPE_FEATURES: Features for shape model (depends on the trained model)

Author: UM & JL
Version: Compact v2.2
V2.2: Possibility of saving classified images
"""

# === IMPORTS ===
from ij import IJ, WindowManager
from ij.measure import ResultsTable
from ij.plugin.frame import RoiManager
from ij.gui import Roi, TextRoi, Overlay
from java.io import FileInputStream, File, FileWriter, BufferedWriter
from java.awt import Color, Font
from java.lang import ClassLoader, Class, Thread
from java.util import ArrayList
from javax.swing import JFileChooser, JOptionPane
import os
import time
import sys

# === UNICODE HANDLING FOR JYTHON ===
# Set default encoding to handle Unicode characters properly
if hasattr(sys, 'setdefaultencoding'):
    sys.setdefaultencoding('utf-8')

# === CROSS-PLATFORM CONFIGURATION ===
def get_config_value(env_var, default_value):
    """Get configuration value from environment variable or use default."""
    return os.environ.get(env_var, default_value)

# === USER CONFIGURATION SECTION ===
# Set these paths according to your system setup

# Path to ImageJ macro file
MACRO_PATH = "C:\Users\<USER>\Fiji.app\macros\extract_features_for_dual_classification.ijm"

# Paths to Weka model files (two separate models)
POROSITY_MODEL_PATH = "C:\Studium\Johann\Partikeluntersuchungen\\63um\\Modelle\\bin_porosity_14_07_b.model"
SHAPE_MODEL_PATH = "C:\Studium\Johann\Partikeluntersuchungen\\63um\\Modelle\\bin_shape_14_07_SCF.model"

# Path to Weka JAR file (optional, if not in ImageJ classpath)
USER_WEKA_JAR_PATH ="C:\Program Files\Weka-3-8-6\weka.jar"

# Input directory containing images to process
IMPORT_DIR = "C:\Studium\Johann\Partikeluntersuchungen\\63um\Test"

# File types to process (semicolon-separated)
FILE_TYPES = "tif"

# Show images during processing
SHOW_IMAGES_DURING_PROCESSING = True

# Print legend in processed image
CREATE_LEGEND = True

# Save processed images with burned-in colorations and legend
SAVE_PROCESSED_IMAGES = True

# Output directory for processed images (if None, saves in same directory as input)
PROCESSED_IMAGES_OUTPUT_DIR = None

# Show particle numbers on colored particles (True = numbered + colored, False = only colored)
SHOW_PARTICLE_NUMBERS = True

# Dual classification configuration with separate feature sets
# Features required for porosity classification
#default_porosity_features = "Concavity; Solidity; Convexity; Roundness; total_black_pixels; FeretRatio"
default_porosity_features = "total_black_pixels"
porosity_features_str = get_config_value("PARTICLE_ANALYSIS_POROSITY_FEATURES", default_porosity_features)
POROSITY_REQUIRED_FEATURES = [feat.strip() for feat in porosity_features_str.split(';')]

# Features required for shape classification (configurable via environment variable)
default_shape_features = "Solidity;Convexity;FeretRatio"
shape_features_str = get_config_value("PARTICLE_ANALYSIS_SHAPE_FEATURES", default_shape_features)
SHAPE_REQUIRED_FEATURES = [feat.strip() for feat in shape_features_str.split(';')]

# Porosity classification labels
POROSITY_LABELS = ["NonPorous", "Porous"]

# Shape classification labels
SHAPE_LABELS = ["Round", "Imperfect"]

# Color scheme for dual classification (porosity x shape combinations)
DUAL_COLORS = {
    ("NonPorous", "Round"): Color(0, 255, 0, 100),      # Green
    ("NonPorous", "Imperfect"): Color(100, 0, 100, 100), # Purple
    ("Porous", "Round"): Color(255, 100, 0, 100),        # Orange
    ("Porous", "Imperfect"): Color(255, 0, 0, 100),     # Red
}

# Global variables
WekaAttribute = None
WekaInstances = None
WekaDenseInstance = None
WekaSerializationHelper = None

# === INTERACTIVE FOLDER SELECTION ===
def is_headless_mode():
    """Check if ImageJ is running in headless mode."""
    try:
        import java.awt.GraphicsEnvironment as GraphicsEnvironment
        return GraphicsEnvironment.isHeadless()
    except:
        return True

def show_folder_selection_dialog():
    """Show folder selection dialog."""
    if is_headless_mode():
        return None
    
    try:
        chooser = JFileChooser()
        chooser.setDialogTitle("Select Directory Containing Images to Analyze")
        chooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY)
        
        # Start in specified default directory (hardcoded)
        default_start_dir = "C:/Studium/Johann/Partikeluntersuchungen"
        if os.path.exists(default_start_dir):
            chooser.setCurrentDirectory(File(default_start_dir))
        else:
            # Fallback to current working directory if default doesn't exist
            chooser.setCurrentDirectory(File(os.getcwd()))
        
        if chooser.showOpenDialog(None) == JFileChooser.APPROVE_OPTION:
            return chooser.getSelectedFile().getAbsolutePath()
    except:
        pass
    return None

def validate_and_select_directory():
    """Validate input directory or show selection dialog."""
    global IMPORT_DIR
    
    if not IMPORT_DIR or not os.path.exists(IMPORT_DIR) or not os.path.isdir(IMPORT_DIR):
        if is_headless_mode():
            IJ.log("ERROR: No valid input directory. Set PARTICLE_ANALYSIS_INPUT_DIR environment variable.")
            return False
        
        selected_dir = show_folder_selection_dialog()
        if not selected_dir:
            IJ.log("ERROR: No directory selected.")
            return False
        
        IMPORT_DIR = selected_dir
        IJ.log("Input directory selected: " + IMPORT_DIR)
    
    return True

# === WEKA SETUP ===
def setup_weka():
    """Setup Weka classes and validate availability."""
    global WekaAttribute, WekaInstances, WekaDenseInstance, WekaSerializationHelper
    
    # Check if Weka is available
    try:
        Class.forName("weka.core.Attribute")
    except:
        if USER_WEKA_JAR_PATH and os.path.exists(USER_WEKA_JAR_PATH):
            try:
                from java.net import URL
                jar_file = File(USER_WEKA_JAR_PATH)
                jar_url = jar_file.toURI().toURL()
                sys_loader = ClassLoader.getSystemClassLoader()
                method = sys_loader.getClass().getDeclaredMethod("addURL", [URL])
                method.setAccessible(True)
                method.invoke(sys_loader, [jar_url])
            except Exception as e:
                IJ.log("ERROR: Failed to load Weka: " + str(e))
                return False
    
    # Import Weka classes
    try:
        from weka.core import Attribute as WekaAttribute
        from weka.core import Instances as WekaInstances
        from weka.core import DenseInstance as WekaDenseInstance
        from weka.core import SerializationHelper as WekaSerializationHelper
        return True
    except Exception as e:
        IJ.log("ERROR: Failed to import Weka classes: " + str(e))
        return False

def load_weka_model(model_path):
    """Load Weka model with error handling."""
    if not os.path.exists(model_path):
        IJ.log("ERROR: Model file not found: " + model_path)
        return None
    
    try:
        file_stream = FileInputStream(model_path)
        loaded_object = WekaSerializationHelper.read(file_stream)
        file_stream.close()
        
        if hasattr(loaded_object, 'classifyInstance'):
            return loaded_object
        else:
            IJ.log("ERROR: Loaded object is not a Weka classifier")
            return None
    except Exception as e:
        error_msg = str(e)
        if "ClassCastException" in error_msg:
            IJ.log("ERROR: Weka version compatibility issue detected")
            IJ.log("SOLUTION: Recreate the model with the current Weka version")
        else:
            IJ.log("ERROR: Failed to load model: " + error_msg)
        return None

# === CORE PROCESSING ===
def run_macro_and_extract_features(imp):
    """Run macro and extract particle features."""
    if not os.path.exists(MACRO_PATH):
        IJ.log("ERROR: Macro file not found: " + MACRO_PATH)
        return None, [], 0
    
    # Clear previous results
    rt = ResultsTable.getResultsTable()
    if rt is not None:
        rt.reset()
    
    roim = RoiManager.getInstance()
    if roim is not None:
        roim.reset()
    
    # Run macro
    try:
        IJ.runMacroFile(MACRO_PATH)
    except Exception as e:
        IJ.log("ERROR: Macro execution failed: " + str(e))
        return None, [], 0
    
    # Get results
    rt = ResultsTable.getResultsTable()
    if rt is None or rt.getCounter() == 0:
        return None, [], 0
    
    # Extract features
    headings = rt.getHeadings()
    excluded_columns = {"label", "id", "x", "y", "bx", "by", "width", "height"}
    feature_headings = [h for h in headings if h.lower() not in excluded_columns]

    # Initialize features_list early
    features_list = []

    for row_idx in range(rt.getCounter()):
        row_features = []
        for heading in feature_headings:
            #IJ.log("Heading:" + str(heading))
            try:
                value = float(rt.getValue(heading, row_idx))
                if str(value).lower() not in ['nan', 'inf', '-inf']:
                    row_features.append(value)
                else:
                    break
            except:
                break
        if len(row_features) == len(feature_headings):
            features_list.append(row_features)

    return features_list, feature_headings, len(features_list)

def create_weka_instances_and_classify_dual(particle_features, feature_names, porosity_classifier, shape_classifier):
    """Create Weka instances and perform dual classification with different feature sets."""

    # Create case-insensitive feature lookup
    feature_names_lower = [f.lower() for f in feature_names]
    feature_name_map = {f.lower(): f for f in feature_names}

    # Validate porosity features (case-insensitive)
    missing_porosity = []
    for req_feat in POROSITY_REQUIRED_FEATURES:
        if req_feat.lower() not in feature_names_lower:
            missing_porosity.append(req_feat)

    if missing_porosity:
        return []

    # Validate shape features (case-insensitive)
    missing_shape = []
    for req_feat in SHAPE_REQUIRED_FEATURES:
        if req_feat.lower() not in feature_names_lower:
            missing_shape.append(req_feat)

    if missing_shape:
        return []

    # Reorder features for porosity classification (case-insensitive)
    porosity_feature_indices = []
    for req_feat in POROSITY_REQUIRED_FEATURES:
        # Find the actual feature name (case-insensitive)
        actual_feat_name = feature_name_map[req_feat.lower()]
        porosity_feature_indices.append(feature_names.index(actual_feat_name))
    porosity_ordered_features = [[row[i] for i in porosity_feature_indices] for row in particle_features]

    # Reorder features for shape classification (case-insensitive)
    shape_feature_indices = []
    for req_feat in SHAPE_REQUIRED_FEATURES:
        # Find the actual feature name (case-insensitive)
        actual_feat_name = feature_name_map[req_feat.lower()]
        shape_feature_indices.append(feature_names.index(actual_feat_name))
    shape_ordered_features = [[row[i] for i in shape_feature_indices] for row in particle_features]

    # Create Weka instances for porosity classification
    porosity_attributes = ArrayList()
    for name in POROSITY_REQUIRED_FEATURES:
        porosity_attributes.add(WekaAttribute(name.replace(" ", "_")))

    porosity_labels_list = ArrayList()
    for label in POROSITY_LABELS:
        porosity_labels_list.add(label)
    porosity_attributes.add(WekaAttribute("Porosity", porosity_labels_list))

    porosity_instances = WekaInstances("PorosityData", porosity_attributes, len(porosity_ordered_features))
    porosity_instances.setClassIndex(porosity_instances.numAttributes() - 1)

    # Create Weka instances for shape classification
    shape_attributes = ArrayList()
    for name in SHAPE_REQUIRED_FEATURES:
        shape_attributes.add(WekaAttribute(name.replace(" ", "_")))

    shape_labels_list = ArrayList()
    for label in SHAPE_LABELS:
        shape_labels_list.add(label)
    shape_attributes.add(WekaAttribute("Shape", shape_labels_list))

    shape_instances = WekaInstances("ShapeData", shape_attributes, len(shape_ordered_features))
    shape_instances.setClassIndex(shape_instances.numAttributes() - 1)

    # Perform dual classification with different feature sets
    dual_predictions = []
    for i in range(len(particle_features)):
        porosity_feature_vector = porosity_ordered_features[i]
        shape_feature_vector = shape_ordered_features[i]

        # Classify porosity using porosity-specific features
        porosity_instance = WekaDenseInstance(porosity_instances.numAttributes())
        porosity_instance.setDataset(porosity_instances)

        # Validate feature count
        expected_feature_count = porosity_instances.numAttributes() - 1
        if len(porosity_feature_vector) != expected_feature_count:
            continue

        for j, value in enumerate(porosity_feature_vector):
            porosity_instance.setValue(j, float(value))
        porosity_instance.setClassMissing()

        # Classify shape using shape-specific features
        shape_instance = WekaDenseInstance(shape_instances.numAttributes())
        shape_instance.setDataset(shape_instances)
        for j, value in enumerate(shape_feature_vector):
            shape_instance.setValue(j, float(value))
        shape_instance.setClassMissing()

        try:
            porosity_result = porosity_classifier.classifyInstance(porosity_instance)
            porosity_index = int(porosity_result)
            porosity_class = POROSITY_LABELS[porosity_index]
        except:
            porosity_class = "unknown"

        try:
            shape_index = int(shape_classifier.classifyInstance(shape_instance))
            shape_class = SHAPE_LABELS[shape_index]
        except:
            shape_class = "unknown"

        dual_predictions.append((porosity_class, shape_class))

    return dual_predictions

def create_legend_overlay(imp):
    """Create a legend overlay showing dual classification colors and classes."""
    try:
        # Legend configuration
        legend_width = 320
        legend_height = 200
        legend_x = 10
        legend_y = 10

        # Create overlay
        overlay = Overlay()

        # Background rectangle (semi-transparent)
        bg_roi = Roi(legend_x, legend_y, legend_width, legend_height)
        bg_roi.setFillColor(Color(255, 255, 255, 180))  # Semi-transparent white
        bg_roi.setStrokeColor(Color.BLACK)
        bg_roi.setStrokeWidth(1)
        overlay.add(bg_roi)

        # Legend title (larger font)
        title_roi = TextRoi(legend_x + 10, legend_y + 10, "Classification Legend")
        title_roi.setCurrentFont(Font("SansSerif", Font.BOLD, 24))
        title_roi.setFillColor(Color.BLACK)
        overlay.add(title_roi)

        # Add color entries (larger spacing and elements)
        y_offset = 50
        entry_height = 36

        # Define legend order: nonporous first, then porous
        legend_order = [
            ("NonPorous", "Round"),
            ("NonPorous", "Imperfect"),
            ("Porous", "Round"),
            ("Porous", "Imperfect")
        ]

        for i, combo in enumerate(legend_order):
            porosity_class, shape_class = combo
            color = DUAL_COLORS.get(combo, Color.GRAY)

            # Color square
            color_roi = Roi(legend_x + 10, legend_y + y_offset + i * entry_height, 24, 24)
            color_roi.setFillColor(color)
            color_roi.setStrokeColor(Color.BLACK)
            color_roi.setStrokeWidth(2)
            overlay.add(color_roi)

            # Text label
            label_text = porosity_class + " + " + shape_class
            text_roi = TextRoi(legend_x + 44, legend_y + y_offset + i * entry_height, label_text + " ")
            text_roi.setCurrentFont(Font("SansSerif", Font.PLAIN, 18))
            text_roi.setFillColor(Color.BLACK)
            overlay.add(text_roi)

        # Set overlay to image
        imp.setOverlay(overlay)

    except Exception as e:
        # Fallback: Create simple text legend using IJ.log
        IJ.log("Legend creation failed, using fallback method: " + str(e))
        create_simple_legend_fallback(imp)

def create_simple_legend_fallback(imp):
    """Fallback legend creation using simple ROI approach."""
    try:
        # Get current ROI manager
        roim = RoiManager.getInstance()
        if roim is None:
            return

        # Store current ROI count to avoid interfering with particle ROIs
        particle_roi_count = roim.getCount()

        # Legend configuration (larger)
        legend_x = 10
        legend_y = 10
        entry_height = 40

        # Add legend title as text ROI (larger font)
        title_roi = TextRoi(legend_x, legend_y, "Legend:")
        title_roi.setCurrentFont(Font("SansSerif", Font.BOLD, 24))
        title_roi.setFillColor(Color.BLACK)
        roim.addRoi(title_roi)

        # Add color entries (larger) - same order as primary method
        legend_order = [
            ("NonPorous", "Round"),
            ("NonPorous", "Imperfect"),
            ("Porous", "Round"),
            ("Porous", "Imperfect")
        ]

        for i, combo in enumerate(legend_order):
            porosity_class, shape_class = combo
            color = DUAL_COLORS.get(combo, Color.GRAY)
            y_pos = legend_y + 40 + i * entry_height

            # Color square (larger)
            color_roi = Roi(legend_x, y_pos, 30, 30)
            color_roi.setFillColor(color)
            color_roi.setStrokeColor(Color.BLACK)
            color_roi.setStrokeWidth(3)
            roim.addRoi(color_roi)

            # Text label (larger font)
            label_text = porosity_class + "+" + shape_class
            text_roi = TextRoi(legend_x + 40, y_pos + 5, label_text)
            text_roi.setCurrentFont(Font("SansSerif", Font.PLAIN, 18))
            text_roi.setFillColor(Color.BLACK)
            roim.addRoi(text_roi)

        # Update display
        imp.updateAndDraw()

    except Exception as e:
        IJ.log("Legend creation completely failed: " + str(e))

def is_image_already_processed(image_path):
    """
    Check if an image has already been processed by looking for existing output files.

    Args:
        image_path: Path to the original image file

    Returns:
        bool: True if already processed, False otherwise
    """
    try:
        # Determine output directory
        if PROCESSED_IMAGES_OUTPUT_DIR and os.path.exists(PROCESSED_IMAGES_OUTPUT_DIR):
            output_dir = PROCESSED_IMAGES_OUTPUT_DIR
        else:
            output_dir = os.path.dirname(image_path)

        # Get original filename without extension
        original_name = os.path.basename(image_path)
        name_without_ext = os.path.splitext(original_name)[0]

        # Check for both possible output files (numbered and colored)
        numbered_filename = "classified_numbered_" + name_without_ext + ".png"
        colored_filename = "classified_colored_" + name_without_ext + ".png"

        numbered_path = os.path.join(output_dir, numbered_filename)
        colored_path = os.path.join(output_dir, colored_filename)

        # If either output file exists, consider it already processed
        if os.path.exists(numbered_path) or os.path.exists(colored_path):
            return True

        return False

    except Exception as e:
        IJ.log("  -> Error checking if image already processed: " + str(e))
        return False  # If error, assume not processed to be safe

def flatten_and_save_image(imp, original_image_path):
    """
    Flatten the image overlays (burn in colorations and legend) and save the processed image.
    Ensures proper handling of numbered vs. colors-only display before flattening.

    Args:
        imp: ImagePlus object with overlays and colored ROIs
        original_image_path: Path to the original image file
    """
    try:
        # CRITICAL: Ensure ROI display is correct before flattening
        roim = RoiManager.getInstance()
        if roim is not None:
            if SHOW_PARTICLE_NUMBERS:
                # Ensure numbers are visible
                roim.runCommand("Show All with labels")
            else:
                # Ensure NO numbers are visible - use multiple methods to be sure
                roim.runCommand("Show All")  # Show ROIs without labels

            # Force update to ensure display changes take effect
            imp.updateAndDraw()

        # Determine output directory
        if PROCESSED_IMAGES_OUTPUT_DIR and os.path.exists(PROCESSED_IMAGES_OUTPUT_DIR):
            output_dir = PROCESSED_IMAGES_OUTPUT_DIR
        else:
            output_dir = os.path.dirname(original_image_path)

        # Create output filename with additions BEFORE the original name
        original_name = os.path.basename(original_image_path)
        name_without_ext = os.path.splitext(original_name)[0]

        if SHOW_PARTICLE_NUMBERS:
            output_filename = "classified_numbered_" + name_without_ext + ".png"
        else:
            output_filename = "classified_colored_" + name_without_ext + ".png"

        output_path = os.path.join(output_dir, output_filename)

        # Flatten the image (burn in overlays and ROI colors)
        IJ.run(imp, "Flatten", "")
        flattened_imp = WindowManager.getCurrentImage()

        if flattened_imp is not None:
            # Save the flattened image as PNG
            IJ.saveAs(flattened_imp, "PNG", output_path)
            mode_text = "numbered + colored" if SHOW_PARTICLE_NUMBERS else "colored only"
            IJ.log("  -> Saved processed image ({}): {}".format(mode_text, output_filename))

            # Keep the PNG open (don't close flattened_imp)
            IJ.log("  -> PNG image kept open for viewing")
        else:
            IJ.log("  -> Warning: Could not create flattened image")

    except Exception as e:
        IJ.log("  -> Error saving processed image: " + str(e))

def color_particles_dual(dual_predictions):
    """Color particles based on dual predictions (porosity, shape)."""
    imp = WindowManager.getCurrentImage()
    roim = RoiManager.getInstance()

    if imp is None or roim is None or roim.getCount() == 0:
        return

    # Color the particles
    for i in range(min(roim.getCount(), len(dual_predictions))):
        roi = roim.getRoi(i)
        porosity_class, shape_class = dual_predictions[i]

        # Get color based on dual classification
        color = DUAL_COLORS.get((porosity_class, shape_class), Color.GRAY)
        roi.setFillColor(color)
        roi.setStrokeColor(color)

    # Handle display based on numbering preference
    if SHOW_PARTICLE_NUMBERS:
        # Show particles with numbers and colors
        roim.runCommand("Show All with labels")

        # Add numbers as overlay elements to ensure they get flattened
        overlay = imp.getOverlay()
        if overlay is None:
            overlay = Overlay()
            imp.setOverlay(overlay)

        # Add number labels to overlay for each ROI
        for i in range(roim.getCount()):
            roi = roim.getRoi(i)
            if roi is not None:
                # Get ROI bounds for label positioning
                bounds = roi.getBounds()
                label_x = bounds.x + bounds.width / 2
                label_y = bounds.y + bounds.height / 2

                # Create text ROI with particle number
                number_text = str(i + 1)  # ROI numbers start from 1
                text_roi = TextRoi(label_x - 10, label_y - 5, number_text)
                text_roi.setCurrentFont(Font("SansSerif", Font.BOLD, 14))
                text_roi.setFillColor(Color.WHITE)
                text_roi.setStrokeColor(Color.BLACK)
                overlay.add(text_roi)

        IJ.log("  -> Particles displayed with numbers and colors (numbers added to overlay)")
    else:
        # Show particles with colors only - ensure no labels/numbers
        roim.runCommand("Show All")
        IJ.log("  -> Particles displayed with colors only")

    imp.updateAndDraw()

    # Add legend overlay (legend colors are independent of particle numbering)
    if CREATE_LEGEND:
        create_legend_overlay(imp)

def process_image_dual(image_path, porosity_classifier, shape_classifier):
    """Process a single image with dual classification."""
    imp = IJ.openImage(image_path)
    if imp is None:
        return None

    if SHOW_IMAGES_DURING_PROCESSING:
        imp.show()
    else:
        WindowManager.setTempCurrentImage(imp)

    # Extract features and classify
    particle_features, feature_names, particle_count = run_macro_and_extract_features(imp)
    if particle_features is None:
        # Always close original TIF image if processing fails (no save dialog)
        imp.changes = False  # Prevent "Save Changes?" dialog
        imp.close()
        IJ.log("  -> Original TIF image closed (processing failed)")
        return None

    dual_predictions = create_weka_instances_and_classify_dual(particle_features, feature_names, porosity_classifier, shape_classifier)
    if dual_predictions:
        # Apply coloring and display particles (with or without numbers based on SHOW_PARTICLE_NUMBERS)
        color_particles_dual(dual_predictions)

        # Save the processed image with burned-in colorations and legend (only once per image)
        if SAVE_PROCESSED_IMAGES:
            flatten_and_save_image(imp, image_path)

    # Count predictions by dual class combinations
    dual_stats = {}
    for porosity_class in POROSITY_LABELS:
        for shape_class in SHAPE_LABELS:
            combo = (porosity_class, shape_class)
            dual_stats[combo] = dual_predictions.count(combo)

    # Also count individual class totals
    porosity_stats = {}
    shape_stats = {}
    for porosity_class in POROSITY_LABELS:
        porosity_stats[porosity_class] = sum(1 for p, s in dual_predictions if p == porosity_class)
    for shape_class in SHAPE_LABELS:
        shape_stats[shape_class] = sum(1 for p, s in dual_predictions if s == shape_class)

    # Always close the original TIF image after processing (regardless of SHOW_IMAGES_DURING_PROCESSING)
    # The PNG output will remain open for viewing
    imp.changes = False  # Prevent "Save Changes?" dialog
    imp.close()
    IJ.log("  -> Original TIF image closed (no save dialog)")

    return {
        'dual': dual_stats,
        'porosity': porosity_stats,
        'shape': shape_stats
    }

# === MAIN PROCESSING ===
def main():
    """Main processing function."""
    start_time = time.time()
    
    IJ.log("\\Clear")
    IJ.log("=" * 60)
    IJ.log("FIJI/ImageJ Compact Batch Particle Classification")
    IJ.log("=" * 60)
    
    # Validate configuration and setup
    if not validate_and_select_directory():
        return False

    if not setup_weka():
        return False

    # Load both classification models
    porosity_classifier = load_weka_model(POROSITY_MODEL_PATH)
    if porosity_classifier is None:
        IJ.log("ERROR: Failed to load porosity model")
        return False

    shape_classifier = load_weka_model(SHAPE_MODEL_PATH)
    if shape_classifier is None:
        IJ.log("ERROR: Failed to load shape model")
        return False

    IJ.log("Both models loaded successfully")

    # Get image files
    try:
        file_types = FILE_TYPES.split(';') if FILE_TYPES else ['tif']
        image_files = []
        for filename in os.listdir(IMPORT_DIR):
            if any(filename.lower().endswith('.' + ft.strip().lower()) for ft in file_types):
                image_files.append(os.path.join(IMPORT_DIR, filename))
        
        if not image_files:
            IJ.log("ERROR: No image files found in directory")
            return False
        
        IJ.log("Found " + str(len(image_files)) + " images to process")
    except Exception as e:
        IJ.log("ERROR: Cannot access input directory: " + str(e))
        return False
    
    # Process images with dual classification
    all_predictions = []

    # Initialize total statistics for dual classification
    total_dual_stats = {}
    total_porosity_stats = {label: 0 for label in POROSITY_LABELS}
    total_shape_stats = {label: 0 for label in SHAPE_LABELS}

    for porosity_class in POROSITY_LABELS:
        for shape_class in SHAPE_LABELS:
            total_dual_stats[(porosity_class, shape_class)] = 0

    processed_count = 0

    for image_path in image_files:
        try:
            image_name = os.path.basename(image_path)

            # Check if image has already been processed
            if is_image_already_processed(image_path):
                IJ.log("SKIPPING (already processed): " + image_name)
                continue

            IJ.log("PROCESSING: " + image_name)
            prediction_stats = process_image_dual(image_path, porosity_classifier, shape_classifier)

            if prediction_stats:
                all_predictions.append((image_name, prediction_stats))

                # Update total statistics
                for combo, count in prediction_stats['dual'].items():
                    total_dual_stats[combo] += count
                for label, count in prediction_stats['porosity'].items():
                    total_porosity_stats[label] += count
                for label, count in prediction_stats['shape'].items():
                    total_shape_stats[label] += count

                processed_count += 1
        except Exception as e:
            IJ.log("ERROR processing " + os.path.basename(image_path) + ": " + str(e))
    
    # Export results to CSV with dual classification
    csv_path = os.path.join(os.path.dirname(IMPORT_DIR), "particle_dual_predictions.csv")
    try:
        writer = BufferedWriter(FileWriter(File(csv_path)))

        # Create header for dual classification
        dual_combo_headers = []
        for porosity_class in POROSITY_LABELS:
            for shape_class in SHAPE_LABELS:
                dual_combo_headers.append(porosity_class + "_" + shape_class)

        porosity_headers = []
        for label in POROSITY_LABELS:
            porosity_headers.append("porosity_" + label)

        shape_headers = []
        for label in SHAPE_LABELS:
            shape_headers.append("shape_" + label)

        header = 'Image,' + ','.join(dual_combo_headers) + ',' + ','.join(porosity_headers) + ',' + ','.join(shape_headers) + '\n'
        writer.write(header)

        # Write data for each image
        for img_name, img_stats in all_predictions:
            row_data = [img_name]

            # Add dual combination counts
            for porosity_class in POROSITY_LABELS:
                for shape_class in SHAPE_LABELS:
                    combo = (porosity_class, shape_class)
                    row_data.append(str(img_stats['dual'].get(combo, 0)))

            # Add porosity totals
            for label in POROSITY_LABELS:
                row_data.append(str(img_stats['porosity'].get(label, 0)))

            # Add shape totals
            for label in SHAPE_LABELS:
                row_data.append(str(img_stats['shape'].get(label, 0)))

            writer.write(','.join(row_data) + '\n')

        # Write totals row
        total_row_data = ['TOTAL']

        # Add dual combination totals
        for porosity_class in POROSITY_LABELS:
            for shape_class in SHAPE_LABELS:
                combo = (porosity_class, shape_class)
                total_row_data.append(str(total_dual_stats.get(combo, 0)))

        # Add porosity totals
        for label in POROSITY_LABELS:
            total_row_data.append(str(total_porosity_stats.get(label, 0)))

        # Add shape totals
        for label in SHAPE_LABELS:
            total_row_data.append(str(total_shape_stats.get(label, 0)))

        writer.write(','.join(total_row_data) + '\n')
        writer.close()
    except Exception as e:
        IJ.log("ERROR: Failed to export CSV: " + str(e))
    
    # Final statistics
    end_time = time.time()
    elapsed = end_time - start_time
    total_particles = sum(total_porosity_stats.values())

    IJ.log("\n" + "=" * 60)
    IJ.log("DUAL CLASSIFICATION PROCESSING COMPLETE")
    IJ.log("=" * 60)
    skipped_count = len(image_files) - processed_count
    IJ.log("Images found: " + str(len(image_files)))
    IJ.log("Images processed: " + str(processed_count))
    IJ.log("Images skipped (already processed): " + str(skipped_count))
    IJ.log("Total particles: " + str(total_particles))
    IJ.log("Processing time: " + str(round(elapsed, 1)) + "s")
    if processed_count > 0:
        IJ.log("Average time per image: " + str(round(elapsed / processed_count, 1)) + "s")

    IJ.log("")
    IJ.log("Porosity Classification Results:")
    for label, count in total_porosity_stats.items():
        percentage = (count / float(total_particles) * 100) if total_particles > 0 else 0
        IJ.log("  " + label + ": " + str(count) + " (" + str(round(percentage, 1)) + "%)")

    IJ.log("")
    IJ.log("Shape Classification Results:")
    for label, count in total_shape_stats.items():
        percentage = (count / float(total_particles) * 100) if total_particles > 0 else 0
        IJ.log("  " + label + ": " + str(count) + " (" + str(round(percentage, 1)) + "%)")

    IJ.log("")
    IJ.log("Dual Classification Combinations:")
    for porosity_class in POROSITY_LABELS:
        for shape_class in SHAPE_LABELS:
            combo = (porosity_class, shape_class)
            count = total_dual_stats.get(combo, 0)
            percentage = (count / float(total_particles) * 100) if total_particles > 0 else 0
            IJ.log("  " + porosity_class + " + " + shape_class + ": " + str(count) + " (" + str(round(percentage, 1)) + "%)")

    IJ.log("Results exported to: " + csv_path)

    # Information about processed images
    if SAVE_PROCESSED_IMAGES:
        if PROCESSED_IMAGES_OUTPUT_DIR and os.path.exists(PROCESSED_IMAGES_OUTPUT_DIR):
            output_dir = PROCESSED_IMAGES_OUTPUT_DIR
        else:
            output_dir = os.path.dirname(IMPORT_DIR)
        IJ.log("Processed images saved to: " + output_dir)
        IJ.log("  -> Images have burned-in colorations and legend")
        IJ.log("  -> Filename format: classified_[original_name].tif")

    IJ.log("=" * 60)
    
    # Cleanup
    # Clear ResultsTable
    rt = ResultsTable.getResultsTable()
    if rt:
        try:
            rt.show("Results")  # Update the window
            IJ.selectWindow("Results")  # Select the Results window
            IJ.run("Close")  # Close using ImageJ command
        except:
            IJ.log("  Warning: Could not close Results table window")

    # Clear RoiManager
    roim = RoiManager.getInstance()
    if roim:
        try:
            IJ.selectWindow("ROI Manager")
            IJ.run("Close")
        except:
            IJ.log("  Warning: Could not close ROI Manager window")

    return True

# === SCRIPT EXECUTION ===
if __name__ == '__main__':
    try:
        success = main()
        if success:
            IJ.log("Script completed successfully!")
        else:
            IJ.log("Script failed. Check the log for details.")
    except Exception as e:
        IJ.log("Unexpected error: " + str(e))
