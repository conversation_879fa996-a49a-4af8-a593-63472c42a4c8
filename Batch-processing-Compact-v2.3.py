"""
FIJI/ImageJ Compact Batch Particle Classification Script v2.3
Automated dual Weka classification (porosity + shape) with color coding, numbering, legend overlay, and statistics export.
Refactored for maximum efficiency while preserving all functionality.
"""

# === IMPORTS ===
from ij import IJ, WindowManager
from ij.measure import ResultsTable
from ij.plugin.frame import RoiManager
from ij.gui import Roi, TextRoi, Overlay
from java.io import FileInputStream, File, FileWriter, BufferedWriter
from java.awt import Color, Font
from java.lang import ClassLoader, Class
from java.util import ArrayList
from javax.swing import JFileChooser
import os, sys, time

# === CONFIGURATION ===

# Paths and settings
MACRO_PATH = "C:\Users\<USER>\Fiji.app\macros\extract_features_for_dual_classification.ijm"
POROSITY_MODEL_PATH = "C:\Studium\Johann\Partikeluntersuchungen\\63um\\Modelle\\bin_porosity_14_07_b.model"
SHAPE_MODEL_PATH = "C:\Studium\Johann\Partikeluntersuchungen\\63um\\Modelle\\bin_shape_14_07_SCF.model"
USER_WEKA_JAR_PATH ="C:\Program Files\Weka-3-8-6\weka.jar"
IMPORT_DIR = "C:\Studium\Johann\Partikeluntersuchungen\\63um\Test"
PROCESSED_IMAGES_OUTPUT_DIR = None
FILE_TYPES = "tif"

# Processing settings
SHOW_IMAGES_DURING_PROCESSING = True
CREATE_LEGEND = False
SAVE_PROCESSED_IMAGES = False
SHOW_PARTICLE_NUMBERS = False

# Features required for porosity classification
default_porosity_features = "total_black_pixels"
POROSITY_REQUIRED_FEATURES = [feat.strip() for feat in default_porosity_features.split(';')]

# Features required for shape classification
default_shape_features = "Solidity;Convexity;FeretRatio"
SHAPE_REQUIRED_FEATURES = [feat.strip() for feat in default_shape_features.split(';')]

# Classification labels and colors
POROSITY_LABELS = ["NonPorous", "Porous"]
SHAPE_LABELS = ["Round", "Imperfect"]
DUAL_COLORS = {
    ("NonPorous", "Round"): Color(0, 255, 0, 100),      # Green
    ("NonPorous", "Imperfect"): Color(100, 0, 100, 100), # Purple
    ("Porous", "Round"): Color(255, 255, 0, 100),       # Yellow
    ("Porous", "Imperfect"): Color(255, 0, 0, 100)      # Red
}

# Global Weka classes
WekaAttribute = WekaInstances = WekaDenseInstance = WekaSerializationHelper = None

# === UTILITY FUNCTIONS ===
def setup_weka_and_validate():
    """Setup Weka classes and validate all required paths."""
    global WekaAttribute, WekaInstances, WekaDenseInstance, WekaSerializationHelper
    
    # Setup Weka
    try:
        Class.forName("weka.core.Attribute")
    except:
        if USER_WEKA_JAR_PATH and os.path.exists(USER_WEKA_JAR_PATH):
            try:
                from java.net import URL
                jar_url = File(USER_WEKA_JAR_PATH).toURI().toURL()
                ClassLoader.getSystemClassLoader().getClass().getDeclaredMethod("addURL", [URL]).invoke(ClassLoader.getSystemClassLoader(), [jar_url])
            except Exception as e:
                raise Exception("Failed to load Weka: " + str(e))
    
    try:
        from weka.core import Attribute as WekaAttribute, Instances as WekaInstances, DenseInstance as WekaDenseInstance, SerializationHelper as WekaSerializationHelper
    except Exception as e:
        raise Exception("Failed to import Weka classes: " + str(e))
    
    # Validate directory
    global IMPORT_DIR
    if not IMPORT_DIR or not os.path.exists(IMPORT_DIR):
        try:
            chooser = JFileChooser()
            chooser.setDialogTitle("Select Directory Containing Images to Analyze")
            chooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY)
            if chooser.showOpenDialog(None) == JFileChooser.APPROVE_OPTION:
                IMPORT_DIR = chooser.getSelectedFile().getAbsolutePath()
            else:
                raise Exception("No directory selected")
        except:
            raise Exception("No valid input directory")
    
    # Validate required files
    for path, name in [(MACRO_PATH, "Macro"), (POROSITY_MODEL_PATH, "Porosity model"), (SHAPE_MODEL_PATH, "Shape model")]:
        if not os.path.exists(path):
            raise Exception("{} file not found: {}".format(name, path))

def load_weka_model(model_path):
    """Load and return Weka model."""
    try:
        file_stream = FileInputStream(model_path)
        model = WekaSerializationHelper.read(file_stream)
        file_stream.close()
        return model if hasattr(model, 'classifyInstance') else None
    except Exception as e:
        IJ.log("ERROR loading model {}: {}".format(model_path, str(e)))
        return None

def is_image_already_processed(image_path):
    """Check if image has already been processed."""
    try:
        output_dir = PROCESSED_IMAGES_OUTPUT_DIR if PROCESSED_IMAGES_OUTPUT_DIR and os.path.exists(PROCESSED_IMAGES_OUTPUT_DIR) else os.path.dirname(image_path)
        name_without_ext = os.path.splitext(os.path.basename(image_path))[0]
        numbered_path = os.path.join(output_dir, "classified_numbered_" + name_without_ext + ".png")
        colored_path = os.path.join(output_dir, "classified_colored_" + name_without_ext + ".png")
        return os.path.exists(numbered_path) or os.path.exists(colored_path)
    except:
        return False

# === CORE PROCESSING FUNCTIONS ===
def run_macro_and_extract_features(imp):
    """Run macro and extract particle features."""
    # Clear previous results
    rt = ResultsTable.getResultsTable()
    if rt: rt.reset()
    roim = RoiManager.getInstance()
    if roim: roim.reset()
    
    # Run macro
    try:
        IJ.runMacroFile(MACRO_PATH)
    except Exception as e:
        IJ.log("ERROR: Macro execution failed: " + str(e))
        return None, [], 0
    
    # Extract features
    rt = ResultsTable.getResultsTable()
    if not rt or rt.getCounter() == 0:
        return None, [], 0
    
    headings = rt.getHeadings()
    excluded_columns = {"label", "id", "x", "y", "bx", "by", "width", "height"}
    feature_headings = [h for h in headings if h.lower() not in excluded_columns]
    
    features_list = []
    for row_idx in range(rt.getCounter()):
        row_features = []
        for heading in feature_headings:
            try:
                value = float(rt.getValue(heading, row_idx))
                if str(value).lower() not in ['nan', 'inf', '-inf']:
                    row_features.append(value)
                else:
                    break
            except:
                break
        if len(row_features) == len(feature_headings):
            features_list.append(row_features)
    
    return features_list, feature_headings, len(features_list)

def create_weka_instances_and_classify_dual(particle_features, feature_names, porosity_classifier, shape_classifier):
    """Create Weka instances and perform dual classification."""
    if not particle_features:
        return []
    
    # Order features for each classifier
    porosity_ordered_features = []
    shape_ordered_features = []
    
    for features in particle_features:
        porosity_row = [features[feature_names.index(name)] for name in POROSITY_REQUIRED_FEATURES if name in feature_names]
        shape_row = [features[feature_names.index(name)] for name in SHAPE_REQUIRED_FEATURES if name in feature_names]
        
        if len(porosity_row) == len(POROSITY_REQUIRED_FEATURES) and len(shape_row) == len(SHAPE_REQUIRED_FEATURES):
            porosity_ordered_features.append(porosity_row)
            shape_ordered_features.append(shape_row)
    
    if not porosity_ordered_features:
        return []
    
    # Create Weka instances for porosity
    porosity_attributes = ArrayList()
    for name in POROSITY_REQUIRED_FEATURES:
        porosity_attributes.add(WekaAttribute(name.replace(" ", "_")))
    porosity_labels_list = ArrayList()
    for label in POROSITY_LABELS:
        porosity_labels_list.add(label)
    porosity_attributes.add(WekaAttribute("Porosity", porosity_labels_list))
    porosity_instances = WekaInstances("PorosityData", porosity_attributes, len(porosity_ordered_features))
    porosity_instances.setClassIndex(porosity_instances.numAttributes() - 1)
    
    # Create Weka instances for shape
    shape_attributes = ArrayList()
    for name in SHAPE_REQUIRED_FEATURES:
        shape_attributes.add(WekaAttribute(name.replace(" ", "_")))
    shape_labels_list = ArrayList()
    for label in SHAPE_LABELS:
        shape_labels_list.add(label)
    shape_attributes.add(WekaAttribute("Shape", shape_labels_list))
    shape_instances = WekaInstances("ShapeData", shape_attributes, len(shape_ordered_features))
    shape_instances.setClassIndex(shape_instances.numAttributes() - 1)
    
    # Perform dual classification
    dual_predictions = []
    for i, (porosity_feature_vector, shape_feature_vector) in enumerate(zip(porosity_ordered_features, shape_ordered_features)):
        # Porosity classification
        porosity_instance = WekaDenseInstance(len(porosity_feature_vector) + 1)
        porosity_instance.setDataset(porosity_instances)
        for j, value in enumerate(porosity_feature_vector):
            porosity_instance.setValue(j, float(value))
        porosity_instance.setClassMissing()
        
        # Shape classification
        shape_instance = WekaDenseInstance(len(shape_feature_vector) + 1)
        shape_instance.setDataset(shape_instances)
        for j, value in enumerate(shape_feature_vector):
            shape_instance.setValue(j, float(value))
        shape_instance.setClassMissing()
        
        try:
            porosity_class = POROSITY_LABELS[int(porosity_classifier.classifyInstance(porosity_instance))]
        except:
            porosity_class = "unknown"
        
        try:
            shape_class = SHAPE_LABELS[int(shape_classifier.classifyInstance(shape_instance))]
        except:
            shape_class = "unknown"
        
        dual_predictions.append((porosity_class, shape_class))
    
    return dual_predictions

def apply_visualization_and_save(imp, dual_predictions, image_path):
    """Consolidated function for coloring, numbering, legend, and saving."""
    roim = RoiManager.getInstance()
    if not roim or roim.getCount() == 0:
        return

    # Color particles
    for i in range(min(roim.getCount(), len(dual_predictions))):
        roi = roim.getRoi(i)
        porosity_class, shape_class = dual_predictions[i]
        color = DUAL_COLORS.get((porosity_class, shape_class), Color.GRAY)
        roi.setFillColor(color)
        roi.setStrokeColor(color)

    roim.runCommand("Show All")

    # Create overlay for numbers and legend
    overlay = Overlay()

    # Add numbers if enabled
    if SHOW_PARTICLE_NUMBERS:
        numbers_added = 0
        for i in range(roim.getCount()):
            roi = roim.getRoi(i)
            if roi:
                bounds = roi.getBounds()
                center_x = bounds.x + bounds.width / 2
                center_y = bounds.y + bounds.height / 2
                text_roi = TextRoi(center_x - 8, center_y - 8, str(i + 1))
                text_roi.setCurrentFont(Font("SansSerif", Font.BOLD, 16))
                text_roi.setFillColor(Color.WHITE)
                text_roi.setStrokeColor(Color.BLACK)
                text_roi.setStrokeWidth(2)
                overlay.add(text_roi)
                numbers_added += 1
        IJ.log("  -> Added {} numbers to overlay".format(numbers_added))

    # Add legend if enabled
    if CREATE_LEGEND:
        legend_x, legend_y = 10, 10
        legend_width, legend_height = 320, 200

        # Background
        bg_roi = Roi(legend_x, legend_y, legend_width, legend_height)
        bg_roi.setFillColor(Color(255, 255, 255, 180))
        bg_roi.setStrokeColor(Color.BLACK)
        overlay.add(bg_roi)

        # Title
        title_roi = TextRoi(legend_x + 10, legend_y + 10, "Classification Legend")
        title_roi.setCurrentFont(Font("SansSerif", Font.BOLD, 24))
        title_roi.setFillColor(Color.BLACK)
        overlay.add(title_roi)

        # Color entries
        y_offset, entry_height = 50, 36
        for i, (porosity_class, shape_class) in enumerate([
            ("NonPorous", "Round"), ("NonPorous", "Imperfect"),
            ("Porous", "Round"), ("Porous", "Imperfect")
        ]):
            color = DUAL_COLORS.get((porosity_class, shape_class), Color.GRAY)

            # Color square
            color_roi = Roi(legend_x + 10, legend_y + y_offset + i * entry_height, 24, 24)
            color_roi.setFillColor(color)
            color_roi.setStrokeColor(Color.BLACK)
            color_roi.setStrokeWidth(2)
            overlay.add(color_roi)

            # Text label
            label_text = porosity_class + " + " + shape_class
            text_roi = TextRoi(legend_x + 44, legend_y + y_offset + i * entry_height, label_text + " ")
            text_roi.setCurrentFont(Font("SansSerif", Font.PLAIN, 18))
            text_roi.setFillColor(Color.BLACK)
            overlay.add(text_roi)

    # Set overlay and update
    imp.setOverlay(overlay)
    imp.updateAndDraw()

    # Save processed image if enabled
    try:
        output_dir = PROCESSED_IMAGES_OUTPUT_DIR if PROCESSED_IMAGES_OUTPUT_DIR and os.path.exists(PROCESSED_IMAGES_OUTPUT_DIR) else os.path.dirname(image_path)
        name_without_ext = os.path.splitext(os.path.basename(image_path))[0]

        if SHOW_PARTICLE_NUMBERS:
            output_filename = "classified_numbered_" + name_without_ext + ".png"
        else:
            output_filename = "classified_colored_" + name_without_ext + ".png"

        output_path = os.path.join(output_dir, output_filename)

        # Flatten and save
        IJ.run(imp, "Flatten", "")
        flattened_imp = WindowManager.getCurrentImage()

        if flattened_imp and SAVE_PROCESSED_IMAGES and SHOW_IMAGES_DURING_PROCESSING:
            IJ.saveAs(flattened_imp, "PNG", output_path)
            mode_text = "numbered + colored" if SHOW_PARTICLE_NUMBERS else "colored only"
            IJ.log("  -> Saved processed image ({}): {}".format(mode_text, output_filename))
        else:
            IJ.log("  -> Warning: Could not create flattened image")
    except Exception as e:
        IJ.log("  -> Error saving processed image: " + str(e))
        

def process_single_image(image_path, porosity_classifier, shape_classifier):
    """Process a single image with dual classification."""
    imp = IJ.openImage(image_path)
    if not imp:
        return None

    # Show or set as temp current image
    if SHOW_IMAGES_DURING_PROCESSING:
        imp.show()
    else:
        WindowManager.setTempCurrentImage(imp)

    # Extract features and classify
    particle_features, feature_names, particle_count = run_macro_and_extract_features(imp)
    if not particle_features:
        imp.changes = False
        imp.close()
        IJ.log("  -> Original TIF image closed (processing failed)")
        return None

    dual_predictions = create_weka_instances_and_classify_dual(particle_features, feature_names, porosity_classifier, shape_classifier)
    if dual_predictions:
        apply_visualization_and_save(imp, dual_predictions, image_path)

    # Calculate statistics
    dual_stats = {}
    for porosity_class in POROSITY_LABELS:
        for shape_class in SHAPE_LABELS:
            combo = (porosity_class, shape_class)
            dual_stats[combo] = dual_predictions.count(combo)

    porosity_stats = {porosity_class: sum(1 for p, s in dual_predictions if p == porosity_class) for porosity_class in POROSITY_LABELS}
    shape_stats = {shape_class: sum(1 for p, s in dual_predictions if s == shape_class) for shape_class in SHAPE_LABELS}

    # Always close original TIF
    imp.changes = False
    imp.close()
    IJ.log("  -> Original TIF image closed (no save dialog)")

    return {'dual': dual_stats, 'porosity': porosity_stats, 'shape': shape_stats}

# === MAIN PROCESSING ===
def main():
    """Main processing function."""
    start_time = time.time()

    IJ.log("\\Clear")
    IJ.log("=" * 60)
    IJ.log("FIJI/ImageJ Compact Batch Particle Classification v2.3")
    IJ.log("=" * 60)

    try:
        # Setup and validation
        setup_weka_and_validate()

        # Load models
        porosity_classifier = load_weka_model(POROSITY_MODEL_PATH)
        shape_classifier = load_weka_model(SHAPE_MODEL_PATH)
        if not porosity_classifier or not shape_classifier:
            raise Exception("Failed to load classification models")

        IJ.log("Both models loaded successfully")

        # Get image files
        file_types = FILE_TYPES.split(';') if FILE_TYPES else ['tif']
        image_files = []
        for filename in os.listdir(IMPORT_DIR):
            if any(filename.lower().endswith('.' + ft.strip().lower()) for ft in file_types):
                image_files.append(os.path.join(IMPORT_DIR, filename))

        if not image_files:
            raise Exception("No image files found in directory")

        IJ.log("Found {} images to process".format(len(image_files)))

        # Initialize statistics
        all_predictions = []
        total_dual_stats = {(p, s): 0 for p in POROSITY_LABELS for s in SHAPE_LABELS}
        total_porosity_stats = {label: 0 for label in POROSITY_LABELS}
        total_shape_stats = {label: 0 for label in SHAPE_LABELS}
        processed_count = 0

        # Process images
        for image_path in image_files:
            try:
                image_name = os.path.basename(image_path)

                # Check if already processed
                if is_image_already_processed(image_path):
                    IJ.log("SKIPPING (already processed): " + image_name)
                    continue

                IJ.log("PROCESSING: " + image_name)
                prediction_stats = process_single_image(image_path, porosity_classifier, shape_classifier)

                if prediction_stats:
                    all_predictions.append((image_name, prediction_stats))

                    # Update totals
                    for combo, count in prediction_stats['dual'].items():
                        total_dual_stats[combo] += count
                    for label, count in prediction_stats['porosity'].items():
                        total_porosity_stats[label] += count
                    for label, count in prediction_stats['shape'].items():
                        total_shape_stats[label] += count

                    processed_count += 1
                    IJ.log("  -> Processed successfully")
                else:
                    IJ.log("  -> Processing failed")

            except Exception as e:
                IJ.log("ERROR processing {}: {}".format(os.path.basename(image_path), str(e)))

        # Export CSV
        csv_path = os.path.join(os.path.dirname(IMPORT_DIR), "particle_dual_predictions.csv")
        try:
            with open(csv_path, 'w') as f:
                # Header
                dual_headers = [p + "_" + s for p in POROSITY_LABELS for s in SHAPE_LABELS]
                porosity_headers = ["porosity_" + label for label in POROSITY_LABELS]
                shape_headers = ["shape_" + label for label in SHAPE_LABELS]
                header = 'Image,' + ','.join(dual_headers) + ',' + ','.join(porosity_headers) + ',' + ','.join(shape_headers) + '\n'
                f.write(header)

                # Data rows
                for img_name, img_stats in all_predictions:
                    row_data = [img_name]
                    for p in POROSITY_LABELS:
                        for s in SHAPE_LABELS:
                            row_data.append(str(img_stats['dual'].get((p, s), 0)))
                    for label in POROSITY_LABELS:
                        row_data.append(str(img_stats['porosity'].get(label, 0)))
                    for label in SHAPE_LABELS:
                        row_data.append(str(img_stats['shape'].get(label, 0)))
                    f.write(','.join(row_data) + '\n')

                # Totals row
                total_row = ['TOTAL']
                for p in POROSITY_LABELS:
                    for s in SHAPE_LABELS:
                        total_row.append(str(total_dual_stats.get((p, s), 0)))
                for label in POROSITY_LABELS:
                    total_row.append(str(total_porosity_stats.get(label, 0)))
                for label in SHAPE_LABELS:
                    total_row.append(str(total_shape_stats.get(label, 0)))
                f.write(','.join(total_row) + '\n')

        except Exception as e:
            IJ.log("ERROR: Failed to export CSV: " + str(e))

        # Final statistics
        end_time = time.time()
        elapsed = end_time - start_time
        total_particles = sum(total_porosity_stats.values())
        skipped_count = len(image_files) - processed_count

        IJ.log("\n" + "=" * 60)
        IJ.log("DUAL CLASSIFICATION PROCESSING COMPLETE")
        IJ.log("=" * 60)
        IJ.log("Images found: {}".format(len(image_files)))
        IJ.log("Images processed: {}".format(processed_count))
        IJ.log("Images skipped (already processed): {}".format(skipped_count))
        IJ.log("Total particles: {}".format(total_particles))
        IJ.log("Processing time: {}s".format(round(elapsed, 1)))
        if processed_count > 0:
            IJ.log("Average time per image: {}s".format(round(elapsed / processed_count, 1)))

        IJ.log("\nPorosity Classification Results:")
        for label, count in total_porosity_stats.items():
            percentage = (count / float(total_particles) * 100) if total_particles > 0 else 0
            IJ.log("  {}: {} ({}%)".format(label, count, round(percentage, 1)))

        IJ.log("\nShape Classification Results:")
        for label, count in total_shape_stats.items():
            percentage = (count / float(total_particles) * 100) if total_particles > 0 else 0
            IJ.log("  {}: {} ({}%)".format(label, count, round(percentage, 1)))

        IJ.log("\nDual Classification Combinations:")
        for porosity_class in POROSITY_LABELS:
            for shape_class in SHAPE_LABELS:
                combo = (porosity_class, shape_class)
                count = total_dual_stats.get(combo, 0)
                percentage = (count / float(total_particles) * 100) if total_particles > 0 else 0
                IJ.log("  {} + {}: {} ({}%)".format(porosity_class, shape_class, count, round(percentage, 1)))

        IJ.log("Results exported to: " + csv_path)

        # Information about particle display mode and processed images
        mode_text = "Numbered + Colored" if SHOW_PARTICLE_NUMBERS else "Colored only"
        IJ.log("Particle display mode: " + mode_text)

        if SAVE_PROCESSED_IMAGES:
            output_dir = PROCESSED_IMAGES_OUTPUT_DIR if PROCESSED_IMAGES_OUTPUT_DIR and os.path.exists(PROCESSED_IMAGES_OUTPUT_DIR) else os.path.dirname(IMPORT_DIR)
            IJ.log("Processed images saved to: " + output_dir)
            IJ.log("  -> Filename format: classified_[original_name].png")

        IJ.log("=" * 60)

        # Cleanup
        # Clear ResultsTable
        rt = ResultsTable.getResultsTable()
        if rt:
            try:
                rt.show("Results")  # Update the window
                IJ.selectWindow("Results")  # Select the Results window
                IJ.run("Close")  # Close using ImageJ command
            except:
                IJ.log("  Warning: Could not close Results table window")

        # Clear RoiManager
        roim = RoiManager.getInstance()
        if roim:
            try:
                IJ.selectWindow("ROI Manager")
                IJ.run("Close")
            except:
                IJ.log("  Warning: Could not close ROI Manager window")

        return True

    except Exception as e:
        IJ.log("ERROR: " + str(e))
        return False
    
    

# === SCRIPT EXECUTION ===
if __name__ == '__main__':
    try:
        success = main()
        if success:
            IJ.log("Script completed successfully!")
        else:
            IJ.log("Script failed. Check the log for details.")
    except Exception as e:
        IJ.log("Unexpected error: " + str(e))
